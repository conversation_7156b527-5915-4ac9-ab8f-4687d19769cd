{"version": 3, "sources": [], "sections": [{"offset": {"line": 15, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/packages/design-system/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@repo/design-system/lib/utils\"\n\nconst buttonVariants = cva(\n  \"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90\",\n        destructive:\n          \"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\n        outline:\n          \"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50\",\n        secondary:\n          \"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80\",\n        ghost:\n          \"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50\",\n        link: \"text-primary underline-offset-4 hover:underline\",\n      },\n      size: {\n        default: \"h-9 px-4 py-2 has-[>svg]:px-3\",\n        sm: \"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5\",\n        lg: \"h-10 rounded-md px-6 has-[>svg]:px-4\",\n        icon: \"size-9\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n      size: \"default\",\n    },\n  }\n)\n\nfunction Button({\n  className,\n  variant,\n  size,\n  asChild = false,\n  ...props\n}: React.ComponentProps<\"button\"> &\n  VariantProps<typeof buttonVariants> & {\n    asChild?: boolean\n  }) {\n  const Comp = asChild ? Slot : \"button\"\n\n  return (\n    <Comp\n      data-slot=\"button\"\n      className={cn(buttonVariants({ variant, size, className }))}\n      {...props}\n    />\n  )\n}\n\nexport { Button, buttonVariants }\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAEA;;;;;AAEA,MAAM,iBAAiB,CAAA,GAAA,2OAAA,CAAA,MAAG,AAAD,EACvB,+bACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,aACE;YACF,SACE;YACF,WACE;YACF,OACE;YACF,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AAGF,SAAS,OAAO,EACd,SAAS,EACT,OAAO,EACP,IAAI,EACJ,UAAU,KAAK,EACf,GAAG,OAIF;IACD,MAAM,OAAO,UAAU,oSAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,6VAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,4IAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACvD,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 72, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/apps/web/app/%5Blocale%5D/%28home%29/components/animated-title.tsx"], "sourcesContent": ["export const AnimatedTitle = () => {\n  return (\n    <h1 className=\"max-w-4xl text-center font-regular text-5xl tracking-tighter md:text-6xl relative z-10\">\n      Built to optimize. Trained to ship.\n    </h1>\n  );\n};\n"], "names": [], "mappings": ";;;;;AAAO,MAAM,gBAAgB;IAC3B,qBACE,6VAAC;QAAG,WAAU;kBAAyF;;;;;;AAI3G", "debugId": null}}, {"offset": {"line": 93, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/apps/web/app/%5Blocale%5D/%28home%29/components/trusted-by.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const TrustedBy = registerClientReference(\n    function() { throw new Error(\"Attempted to call TrustedBy() from the server but TrustedBy is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/apps/web/app/[locale]/(home)/components/trusted-by.tsx <module evaluation>\",\n    \"TrustedBy\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,YAAY,CAAA,GAAA,oWAAA,CAAA,0BAAuB,AAAD,EAC3C;IAAa,MAAM,IAAI,MAAM;AAAkO,GAC/P,wFACA", "debugId": null}}, {"offset": {"line": 107, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/apps/web/app/%5Blocale%5D/%28home%29/components/trusted-by.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const TrustedBy = registerClientReference(\n    function() { throw new Error(\"Attempted to call TrustedBy() from the server but TrustedBy is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/apps/web/app/[locale]/(home)/components/trusted-by.tsx\",\n    \"TrustedBy\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,YAAY,CAAA,GAAA,oWAAA,CAAA,0BAAuB,AAAD,EAC3C;IAAa,MAAM,IAAI,MAAM;AAAkO,GAC/P,oEACA", "debugId": null}}, {"offset": {"line": 121, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 131, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/apps/web/app/%5Blocale%5D/%28home%29/components/hero.tsx"], "sourcesContent": ["import { env } from '@/env';\nimport { blog } from '@repo/cms';\nimport { Feed } from '@repo/cms/components/feed';\nimport { Button } from '@repo/design-system/components/ui/button';\nimport type { Dictionary } from '@repo/internationalization';\nimport { ExternalLink, MoveRight, PhoneCall } from 'lucide-react';\nimport Link from 'next/link';\nimport Image from 'next/image';\nimport { AnimatedTitle } from './animated-title';\nimport { TrustedBy } from './trusted-by';\n\ntype HeroProps = {\n  dictionary: Dictionary;\n};\n\n// Hero component for the homepage - updated\nexport const Hero = async ({ dictionary }: HeroProps) => (\n  <div className=\"w-full relative overflow-hidden\">\n\n\n\n\n\n\n\n\n    <div className=\"container mx-auto px-4 sm:px-6 lg:px-8\">\n      <div className=\"flex flex-col items-center justify-center gap-8 pt-6 pb-8 lg:pt-12 lg:pb-12\">\n        <div>\n          <Feed queries={[blog.latestPostQuery]}>\n            {/* biome-ignore lint/suspicious/useAwait: \"Server Actions must be async\" */}\n            {async ([data]: [any]) => {\n              'use server';\n\n              return (\n                <Button variant=\"secondary\" size=\"sm\" className=\"gap-4 bg-gradient-to-r from-white/10 to-white/10 border-white/20 hover:from-white/20 hover:to-white/20 hover:border-white/30 text-white hover:text-white/90\" asChild>\n                  <Link href={`/blog/${data.blog.posts.item?._slug}`}>\n                    {dictionary.web.home.hero.announcement}{' '}\n                    <MoveRight className=\"h-4 w-4 text-white\" />\n                  </Link>\n                </Button>\n              );\n            }}\n          </Feed>\n        </div>\n        <div className=\"flex flex-col gap-6 relative\">\n          <div className=\"flex flex-col gap-6 relative\">\n            <AnimatedTitle />\n            <p className=\"max-w-3xl mx-auto text-center text-lg text-muted-foreground leading-relaxed tracking-tight md:text-xl relative z-10\">\n              Meet Cubent.Dev, your AI coding partner inside the editor. Generate code, solve bugs, document faster, and build smarter with simple language.\n            </p>\n          </div>\n          <div className=\"flex flex-col sm:flex-row gap-4 mt-8 justify-center\">\n          <Button\n            variant=\"outline\"\n            className=\"bg-neutral-800/70 border-neutral-600 text-white hover:bg-neutral-700/70 backdrop-blur-sm px-12 py-6 text-2xl font-medium sm:bg-neutral-900/50 sm:border-neutral-700 sm:hover:bg-neutral-800/50 sm:px-10 sm:py-5 sm:text-xl\"\n            asChild\n          >\n            <Link href=\"https://marketplace.visualstudio.com/items?itemName=cubent.cubent\" target=\"_blank\" rel=\"noopener noreferrer\" className=\"flex items-center gap-4 sm:gap-3\">\n              <svg className=\"w-8 h-8 sm:w-7 sm:h-7\" viewBox=\"0 0 24 24\" fill=\"currentColor\">\n                <path d=\"M23.15 2.587L18.21.21a1.494 1.494 0 0 0-1.705.29l-9.46 8.63-4.12-3.128a.999.999 0 0 0-1.276.057L.327 7.261A1 1 0 0 0 .326 8.74L3.899 12 .326 15.26a1 1 0 0 0 .001 1.479L1.65 17.94a.999.999 0 0 0 1.276.057l4.12-3.128 9.46 8.63a1.492 1.492 0 0 0 1.704.29l4.942-2.377A1.5 1.5 0 0 0 24 20.06V3.939a1.5 1.5 0 0 0-.85-1.352zm-5.146 14.861L10.826 12l7.178-5.448v10.896z\"/>\n              </svg>\n              VS Code\n              <ExternalLink className=\"w-7 h-7 sm:w-6 sm:h-6\" />\n            </Link>\n          </Button>\n          <Button\n            variant=\"outline\"\n            className=\"bg-neutral-800/70 border-neutral-600 text-white hover:bg-neutral-700/70 backdrop-blur-sm px-12 py-6 text-2xl font-medium sm:bg-neutral-900/50 sm:border-neutral-700 sm:hover:bg-neutral-800/50 sm:px-10 sm:py-5 sm:text-xl\"\n            asChild\n          >\n            <Link href=\"https://plugins.jetbrains.com/plugin/cubent\" target=\"_blank\" rel=\"noopener noreferrer\" className=\"flex items-center gap-4 sm:gap-3\">\n              <svg className=\"w-8 h-8 sm:w-7 sm:h-7\" viewBox=\"0 0 24 24\" fill=\"currentColor\">\n                <rect x=\"0\" y=\"0\" width=\"24\" height=\"24\" fill=\"currentColor\"/>\n                <rect x=\"2\" y=\"2\" width=\"20\" height=\"20\" fill=\"white\"/>\n                <text x=\"3\" y=\"8\" fontSize=\"6\" fontWeight=\"bold\" fill=\"black\" fontFamily=\"Arial, sans-serif\">JET</text>\n                <text x=\"3\" y=\"15\" fontSize=\"6\" fontWeight=\"bold\" fill=\"black\" fontFamily=\"Arial, sans-serif\">BRAINS</text>\n                <rect x=\"3\" y=\"18\" width=\"10\" height=\"1.5\" fill=\"black\"/>\n              </svg>\n              JetBrains\n              <ExternalLink className=\"w-7 h-7 sm:w-6 sm:h-6\" />\n            </Link>\n          </Button>\n          </div>\n        </div>\n      </div>\n    </div>\n\n    {/* Company logos section moved above GIF */}\n    <TrustedBy dictionary={dictionary} />\n\n\n\n\n\n\n\n\n\n\n\n\n  </div>\n);\n"], "names": [], "mappings": ";;;;;;;AACA;AACA;AAAA;AACA;AAEA;AAAA;AACA;AAEA;AACA;;;;;;;;;;;MAsBa,wEAAO,CAAC,KAAY;;IAGnB,qBACE,6VAAC,2JAAA,CAAA,SAAM;QAAC,SAAQ;QAAY,MAAK;QAAK,WAAU;QAA8J,OAAO;kBACnN,cAAA,6VAAC,2QAAA,CAAA,UAAI;YAAC,MAAM,CAAC,MAAM,EAAE,KAAK,IAAI,CAAC,KAAK,CAAC,IAAI,EAAE,OAAO;;;gBACR;8BACxC,6VAAC,oSAAA,CAAA,YAAS;oBAAC,WAAU;;;;;;;;;;;;;;;;;AAI7B;AA1BL,MAAM,OAAO,OAAO,EAAE,UAAU,EAAa,iBAClD,6VAAC;QAAI,WAAU;;0BASb,6VAAC;gBAAI,WAAU;0BACb,cAAA,6VAAC;oBAAI,WAAU;;sCACb,6VAAC;sCACC,cAAA,6VAAC,sLAAA,CAAA,OAAI;gCAAC,SAAS;oCAAC,wHAAA,CAAA,OAAI,CAAC,eAAe;iCAAC;0CAElC,8VAAA;;;;;;;;;;;sCAcL,6VAAC;4BAAI,WAAU;;8CACb,6VAAC;oCAAI,WAAU;;sDACb,6VAAC,gLAAA,CAAA,gBAAa;;;;;sDACd,6VAAC;4CAAE,WAAU;sDAAsH;;;;;;;;;;;;8CAIrI,6VAAC;oCAAI,WAAU;;sDACf,6VAAC,2JAAA,CAAA,SAAM;4CACL,SAAQ;4CACR,WAAU;4CACV,OAAO;sDAEP,cAAA,6VAAC,2QAAA,CAAA,UAAI;gDAAC,MAAK;gDAAoE,QAAO;gDAAS,KAAI;gDAAsB,WAAU;;kEACjI,6VAAC;wDAAI,WAAU;wDAAwB,SAAQ;wDAAY,MAAK;kEAC9D,cAAA,6VAAC;4DAAK,GAAE;;;;;;;;;;;oDACJ;kEAEN,6VAAC,0SAAA,CAAA,eAAY;wDAAC,WAAU;;;;;;;;;;;;;;;;;sDAG5B,6VAAC,2JAAA,CAAA,SAAM;4CACL,SAAQ;4CACR,WAAU;4CACV,OAAO;sDAEP,cAAA,6VAAC,2QAAA,CAAA,UAAI;gDAAC,MAAK;gDAA8C,QAAO;gDAAS,KAAI;gDAAsB,WAAU;;kEAC3G,6VAAC;wDAAI,WAAU;wDAAwB,SAAQ;wDAAY,MAAK;;0EAC9D,6VAAC;gEAAK,GAAE;gEAAI,GAAE;gEAAI,OAAM;gEAAK,QAAO;gEAAK,MAAK;;;;;;0EAC9C,6VAAC;gEAAK,GAAE;gEAAI,GAAE;gEAAI,OAAM;gEAAK,QAAO;gEAAK,MAAK;;;;;;0EAC9C,6VAAC;gEAAK,GAAE;gEAAI,GAAE;gEAAI,UAAS;gEAAI,YAAW;gEAAO,MAAK;gEAAQ,YAAW;0EAAoB;;;;;;0EAC7F,6VAAC;gEAAK,GAAE;gEAAI,GAAE;gEAAK,UAAS;gEAAI,YAAW;gEAAO,MAAK;gEAAQ,YAAW;0EAAoB;;;;;;0EAC9F,6VAAC;gEAAK,GAAE;gEAAI,GAAE;gEAAK,OAAM;gEAAK,QAAO;gEAAM,MAAK;;;;;;;;;;;;oDAC5C;kEAEN,6VAAC,0SAAA,CAAA,eAAY;wDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BASlC,6VAAC,4KAAA,CAAA,YAAS;gBAAC,YAAY", "debugId": null}}, {"offset": {"line": 496, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/packages/analytics/posthog/server.ts"], "sourcesContent": ["import 'server-only';\nimport { PostHog } from 'posthog-node';\nimport { keys } from '../keys';\n\nconst envKeys = keys();\n\nexport const analytics = envKeys.NEXT_PUBLIC_POSTHOG_KEY && envKeys.NEXT_PUBLIC_POSTHOG_HOST\n  ? new PostHog(envKeys.NEXT_PUBLIC_POSTHOG_KEY, {\n      host: envKeys.NEXT_PUBLIC_POSTHOG_HOST,\n\n      // Don't batch events and flush immediately - we're running in a serverless environment\n      flushAt: 1,\n      flushInterval: 0,\n    })\n  : null;\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;;;;AAEA,MAAM,UAAU,CAAA,GAAA,6HAAA,CAAA,OAAI,AAAD;AAEZ,MAAM,YAAY,QAAQ,uBAAuB,IAAI,QAAQ,wBAAwB,GACxF,IAAI,qNAAA,CAAA,UAAO,CAAC,QAAQ,uBAAuB,EAAE;IAC3C,MAAM,QAAQ,wBAAwB;IAEtC,uFAAuF;IACvF,SAAS;IACT,eAAe;AACjB,KACA", "debugId": null}}, {"offset": {"line": 518, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/packages/auth/server.ts"], "sourcesContent": ["import 'server-only';\n\nexport * from '@clerk/nextjs/server';\n"], "names": [], "mappings": ";AAAA", "debugId": null}}, {"offset": {"line": 555, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/packages/feature-flags/lib/create-flag.ts"], "sourcesContent": ["import { analytics } from '@repo/analytics/posthog/server';\nimport { auth } from '@repo/auth/server';\nimport { flag } from 'flags/next';\n\nexport const createFlag = (key: string) =>\n  flag({\n    key,\n    defaultValue: false,\n    async decide() {\n      const { userId } = await auth();\n\n      if (!userId) {\n        return this.defaultValue as boolean;\n      }\n\n      const isEnabled = analytics\n        ? await analytics.isFeatureEnabled(key, userId)\n        : null;\n\n      return isEnabled ?? (this.defaultValue as boolean);\n    },\n  });\n"], "names": [], "mappings": ";;;AAAA;AACA;AAAA;AACA;;;;AAEO,MAAM,aAAa,CAAC,MACzB,CAAA,GAAA,8OAAA,CAAA,OAAI,AAAD,EAAE;QACH;QACA,cAAc;QACd,MAAM;YACJ,MAAM,EAAE,MAAM,EAAE,GAAG,MAAM,CAAA,GAAA,6RAAA,CAAA,OAAI,AAAD;YAE5B,IAAI,CAAC,QAAQ;gBACX,OAAO,IAAI,CAAC,YAAY;YAC1B;YAEA,MAAM,YAAY,0IAAA,CAAA,YAAS,GACvB,MAAM,0IAAA,CAAA,YAAS,CAAC,gBAAgB,CAAC,KAAK,UACtC;YAEJ,OAAO,aAAc,IAAI,CAAC,YAAY;QACxC;IACF", "debugId": null}}, {"offset": {"line": 583, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/packages/feature-flags/index.ts"], "sourcesContent": ["import { createFlag } from './lib/create-flag';\n\nexport const showBetaFeature = createFlag('showBetaFeature');\n"], "names": [], "mappings": ";;;AAAA;;AAEO,MAAM,kBAAkB,CAAA,GAAA,qJAAA,CAAA,aAAU,AAAD,EAAE", "debugId": null}}, {"offset": {"line": 595, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/packages/seo/metadata.ts"], "sourcesContent": ["import merge from 'lodash.merge';\nimport type { Metadata } from 'next';\n\ntype MetadataGenerator = Omit<Metadata, 'description' | 'title'> & {\n  title: string;\n  description: string;\n  image?: string;\n};\n\nconst applicationName = 'Cubent';\nconst author: Metadata['authors'] = {\n  name: '<PERSON><PERSON><PERSON>',\n  url: 'https://cubent.dev/',\n};\nconst publisher = 'Cubent';\nconst twitterHandle = '@cubent';\nconst protocol = process.env.NODE_ENV === 'production' ? 'https' : 'http';\nconst productionUrl = process.env.VERCEL_PROJECT_PRODUCTION_URL;\n\nexport const createMetadata = ({\n  title,\n  description,\n  image,\n  ...properties\n}: MetadataGenerator): Metadata => {\n  const parsedTitle = `${title} | ${applicationName}`;\n  const defaultMetadata: Metadata = {\n    title: parsedTitle,\n    description,\n    applicationName,\n    metadataBase: productionUrl\n      ? new URL(`${protocol}://${productionUrl}`)\n      : undefined,\n    authors: [author],\n    creator: author.name,\n    formatDetection: {\n      telephone: false,\n    },\n    appleWebApp: {\n      capable: true,\n      statusBarStyle: 'default',\n      title: parsedTitle,\n    },\n    openGraph: {\n      title: parsedTitle,\n      description,\n      type: 'website',\n      siteName: applicationName,\n      locale: 'en_US',\n    },\n    publisher,\n    twitter: {\n      card: 'summary_large_image',\n      creator: twitterHandle,\n    },\n  };\n\n  const metadata: Metadata = merge(defaultMetadata, properties);\n\n  if (image && metadata.openGraph) {\n    metadata.openGraph.images = [\n      {\n        url: image,\n        width: 1200,\n        height: 630,\n        alt: title,\n      },\n    ];\n  }\n\n  return metadata;\n};\n"], "names": [], "mappings": ";;;AAAA;;AASA,MAAM,kBAAkB;AACxB,MAAM,SAA8B;IAClC,MAAM;IACN,KAAK;AACP;AACA,MAAM,YAAY;AAClB,MAAM,gBAAgB;AACtB,MAAM,WAAW,6EAAkD;AACnE,MAAM,gBAAgB,QAAQ,GAAG,CAAC,6BAA6B;AAExD,MAAM,iBAAiB,CAAC,EAC7B,KAAK,EACL,WAAW,EACX,KAAK,EACL,GAAG,YACe;IAClB,MAAM,cAAc,GAAG,MAAM,GAAG,EAAE,iBAAiB;IACnD,MAAM,kBAA4B;QAChC,OAAO;QACP;QACA;QACA,cAAc,gBACV,IAAI,IAAI,GAAG,SAAS,GAAG,EAAE,eAAe,IACxC;QACJ,SAAS;YAAC;SAAO;QACjB,SAAS,OAAO,IAAI;QACpB,iBAAiB;YACf,WAAW;QACb;QACA,aAAa;YACX,SAAS;YACT,gBAAgB;YAChB,OAAO;QACT;QACA,WAAW;YACT,OAAO;YACP;YACA,MAAM;YACN,UAAU;YACV,QAAQ;QACV;QACA;QACA,SAAS;YACP,MAAM;YACN,SAAS;QACX;IACF;IAEA,MAAM,WAAqB,CAAA,GAAA,oMAAA,CAAA,UAAK,AAAD,EAAE,iBAAiB;IAElD,IAAI,SAAS,SAAS,SAAS,EAAE;QAC/B,SAAS,SAAS,CAAC,MAAM,GAAG;YAC1B;gBACE,KAAK;gBACL,OAAO;gBACP,QAAQ;gBACR,KAAK;YACP;SACD;IACH;IAEA,OAAO;AACT", "debugId": null}}, {"offset": {"line": 660, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/apps/web/app/%5Blocale%5D/%28home%29/components/community.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const Community = registerClientReference(\n    function() { throw new Error(\"Attempted to call Community() from the server but Community is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/apps/web/app/[locale]/(home)/components/community.tsx <module evaluation>\",\n    \"Community\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,YAAY,CAAA,GAAA,oWAAA,CAAA,0BAAuB,AAAD,EAC3C;IAAa,MAAM,IAAI,MAAM;AAAkO,GAC/P,uFACA", "debugId": null}}, {"offset": {"line": 674, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/apps/web/app/%5Blocale%5D/%28home%29/components/community.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const Community = registerClientReference(\n    function() { throw new Error(\"Attempted to call Community() from the server but Community is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/apps/web/app/[locale]/(home)/components/community.tsx\",\n    \"Community\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,YAAY,CAAA,GAAA,oWAAA,CAAA,0BAAuB,AAAD,EAC3C;IAAa,MAAM,IAAI,MAAM;AAAkO,GAC/P,mEACA", "debugId": null}}, {"offset": {"line": 688, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 698, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/apps/web/app/%5Blocale%5D/%28home%29/components/download.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const Download = registerClientReference(\n    function() { throw new Error(\"Attempted to call Download() from the server but Download is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/apps/web/app/[locale]/(home)/components/download.tsx <module evaluation>\",\n    \"Download\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,WAAW,CAAA,GAAA,oWAAA,CAAA,0BAAuB,AAAD,EAC1C;IAAa,MAAM,IAAI,MAAM;AAAgO,GAC7P,sFACA", "debugId": null}}, {"offset": {"line": 712, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/apps/web/app/%5Blocale%5D/%28home%29/components/download.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const Download = registerClientReference(\n    function() { throw new Error(\"Attempted to call Download() from the server but Download is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/apps/web/app/[locale]/(home)/components/download.tsx\",\n    \"Download\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,WAAW,CAAA,GAAA,oWAAA,CAAA,0BAAuB,AAAD,EAC1C;IAAa,MAAM,IAAI,MAAM;AAAgO,GAC7P,kEACA", "debugId": null}}, {"offset": {"line": 726, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 736, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/apps/web/app/%5Blocale%5D/%28home%29/components/mockup.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const Mockup = registerClientReference(\n    function() { throw new Error(\"Attempted to call Mockup() from the server but <PERSON><PERSON><PERSON> is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/apps/web/app/[locale]/(home)/components/mockup.tsx <module evaluation>\",\n    \"Mockup\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,SAAS,CAAA,GAAA,oWAAA,CAAA,0BAAuB,AAAD,EACxC;IAAa,MAAM,IAAI,MAAM;AAA4N,GACzP,oFACA", "debugId": null}}, {"offset": {"line": 750, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/apps/web/app/%5Blocale%5D/%28home%29/components/mockup.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const Mockup = registerClientReference(\n    function() { throw new Error(\"Attempted to call Mockup() from the server but <PERSON><PERSON><PERSON> is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/apps/web/app/[locale]/(home)/components/mockup.tsx\",\n    \"Mockup\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,SAAS,CAAA,GAAA,oWAAA,CAAA,0BAAuB,AAAD,EACxC;IAAa,MAAM,IAAI,MAAM;AAA4N,GACzP,gEACA", "debugId": null}}, {"offset": {"line": 773, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/apps/web/app/%5Blocale%5D/%28home%29/components/model-providers.tsx"], "sourcesContent": ["import React from 'react';\n\nexport const ModelProviders = () => {\n  return (\n    <div className=\"w-full py-20 lg:py-32\" style={{ backgroundColor: '#161616' }}>\n      <div className=\"container mx-auto px-4 sm:px-6 lg:px-8\">\n        <div className=\"flex flex-col items-center justify-center gap-12\">\n          {/* Header */}\n          <div className=\"flex flex-col gap-4 text-center max-w-4xl mx-auto\">\n            <h2 className=\"font-regular text-3xl tracking-tighter md:text-4xl\">\n              First-class support for every major model provider\n            </h2>\n            <p className=\"max-w-2xl mx-auto text-lg text-muted-foreground leading-relaxed tracking-tight\">\n              Connect with the AI models you trust. Cubent works seamlessly with all leading providers.\n            </p>\n          </div>\n\n          {/* Model Provider Logos */}\n          <div className=\"w-full max-w-6xl mx-auto\">\n            <div className=\"flex flex-wrap items-center justify-center gap-8 md:gap-12 lg:gap-16\">\n\n              {/* OpenAI */}\n              <div className=\"flex items-center gap-3 group\">\n                <div className=\"w-12 h-12 flex items-center justify-center\">\n                  <img\n                    src=\"https://registry.npmmirror.com/@lobehub/icons-static-png/latest/files/dark/openai.png\"\n                    alt=\"OpenAI\"\n                    className=\"w-10 h-10 grayscale opacity-50 group-hover:opacity-80 transition-opacity duration-300\"\n                  />\n                </div>\n                <span className=\"text-lg font-medium text-muted-foreground\">OpenAI</span>\n              </div>\n\n              {/* Anthropic */}\n              <div className=\"flex items-center gap-3 group\">\n                <div className=\"w-12 h-12 flex items-center justify-center\">\n                  <img\n                    src=\"https://registry.npmmirror.com/@lobehub/icons-static-png/latest/files/dark/anthropic.png\"\n                    alt=\"Anthropic\"\n                    className=\"w-10 h-10 grayscale opacity-50 group-hover:opacity-80 transition-opacity duration-300\"\n                  />\n                </div>\n                <span className=\"text-lg font-medium text-muted-foreground\">Anthropic</span>\n              </div>\n\n              {/* Google */}\n              <div className=\"flex items-center gap-3 group\">\n                <div className=\"w-12 h-12 flex items-center justify-center\">\n                  <img\n                    src=\"https://registry.npmmirror.com/@lobehub/icons-static-png/latest/files/dark/gemini-color.png\"\n                    alt=\"Google Gemini\"\n                    className=\"w-10 h-10 grayscale opacity-50 group-hover:opacity-80 transition-opacity duration-300\"\n                  />\n                </div>\n                <span className=\"text-lg font-medium text-muted-foreground\">Google</span>\n              </div>\n\n              {/* Cohere */}\n              <div className=\"flex items-center gap-3 group\">\n                <div className=\"w-12 h-12 flex items-center justify-center\">\n                  <img\n                    src=\"https://registry.npmmirror.com/@lobehub/icons-static-png/latest/files/dark/cohere-color.png\"\n                    alt=\"Cohere\"\n                    className=\"w-10 h-10 grayscale opacity-50 group-hover:opacity-80 transition-opacity duration-300\"\n                  />\n                </div>\n                <span className=\"text-lg font-medium text-muted-foreground\">Cohere</span>\n              </div>\n\n              {/* Mistral */}\n              <div className=\"flex items-center gap-3 group\">\n                <div className=\"w-12 h-12 flex items-center justify-center\">\n                  <img\n                    src=\"https://registry.npmmirror.com/@lobehub/icons-static-png/latest/files/dark/mistral-color.png\"\n                    alt=\"Mistral\"\n                    className=\"w-10 h-10 grayscale opacity-50 group-hover:opacity-80 transition-opacity duration-300\"\n                  />\n                </div>\n                <span className=\"text-lg font-medium text-muted-foreground\">Mistral</span>\n              </div>\n\n              {/* OpenRouter */}\n              <div className=\"flex items-center gap-3 group\">\n                <div className=\"w-12 h-12 flex items-center justify-center\">\n                  <img\n                    src=\"https://registry.npmmirror.com/@lobehub/icons-static-png/latest/files/dark/openrouter.png\"\n                    alt=\"OpenRouter\"\n                    className=\"w-10 h-10 grayscale opacity-50 group-hover:opacity-80 transition-opacity duration-300\"\n                  />\n                </div>\n                <span className=\"text-lg font-medium text-muted-foreground\">OpenRouter</span>\n              </div>\n\n            </div>\n          </div>\n\n\n        </div>\n      </div>\n    </div>\n  );\n};\n"], "names": [], "mappings": ";;;;;AAEO,MAAM,iBAAiB;IAC5B,qBACE,6VAAC;QAAI,WAAU;QAAwB,OAAO;YAAE,iBAAiB;QAAU;kBACzE,cAAA,6VAAC;YAAI,WAAU;sBACb,cAAA,6VAAC;gBAAI,WAAU;;kCAEb,6VAAC;wBAAI,WAAU;;0CACb,6VAAC;gCAAG,WAAU;0CAAqD;;;;;;0CAGnE,6VAAC;gCAAE,WAAU;0CAAiF;;;;;;;;;;;;kCAMhG,6VAAC;wBAAI,WAAU;kCACb,cAAA,6VAAC;4BAAI,WAAU;;8CAGb,6VAAC;oCAAI,WAAU;;sDACb,6VAAC;4CAAI,WAAU;sDACb,cAAA,6VAAC;gDACC,KAAI;gDACJ,KAAI;gDACJ,WAAU;;;;;;;;;;;sDAGd,6VAAC;4CAAK,WAAU;sDAA4C;;;;;;;;;;;;8CAI9D,6VAAC;oCAAI,WAAU;;sDACb,6VAAC;4CAAI,WAAU;sDACb,cAAA,6VAAC;gDACC,KAAI;gDACJ,KAAI;gDACJ,WAAU;;;;;;;;;;;sDAGd,6VAAC;4CAAK,WAAU;sDAA4C;;;;;;;;;;;;8CAI9D,6VAAC;oCAAI,WAAU;;sDACb,6VAAC;4CAAI,WAAU;sDACb,cAAA,6VAAC;gDACC,KAAI;gDACJ,KAAI;gDACJ,WAAU;;;;;;;;;;;sDAGd,6VAAC;4CAAK,WAAU;sDAA4C;;;;;;;;;;;;8CAI9D,6VAAC;oCAAI,WAAU;;sDACb,6VAAC;4CAAI,WAAU;sDACb,cAAA,6VAAC;gDACC,KAAI;gDACJ,KAAI;gDACJ,WAAU;;;;;;;;;;;sDAGd,6VAAC;4CAAK,WAAU;sDAA4C;;;;;;;;;;;;8CAI9D,6VAAC;oCAAI,WAAU;;sDACb,6VAAC;4CAAI,WAAU;sDACb,cAAA,6VAAC;gDACC,KAAI;gDACJ,KAAI;gDACJ,WAAU;;;;;;;;;;;sDAGd,6VAAC;4CAAK,WAAU;sDAA4C;;;;;;;;;;;;8CAI9D,6VAAC;oCAAI,WAAU;;sDACb,6VAAC;4CAAI,WAAU;sDACb,cAAA,6VAAC;gDACC,KAAI;gDACJ,KAAI;gDACJ,WAAU;;;;;;;;;;;sDAGd,6VAAC;4CAAK,WAAU;sDAA4C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAW5E", "debugId": null}}, {"offset": {"line": 1051, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/apps/web/app/%5Blocale%5D/%28home%29/page.tsx"], "sourcesContent": ["import { showBetaFeature } from '@repo/feature-flags';\nimport { getDictionary } from '@repo/internationalization';\nimport { createMetadata } from '@repo/seo/metadata';\nimport type { Metadata } from 'next';\nimport Image from 'next/image';\nimport { Community } from './components/community';\nimport { CTA } from './components/cta';\nimport { Download } from './components/download';\nimport { FAQ } from './components/faq';\n\nimport { Hero } from './components/hero';\nimport { Mockup } from './components/mockup';\nimport { ModelProviders } from './components/model-providers';\nimport { Stats } from './components/stats';\nimport { Testimonials } from './components/testimonials';\nimport { TrustedBy } from './components/trusted-by';\n\n\n\ntype HomeProps = {\n  params: Promise<{\n    locale: string;\n  }>;\n};\n\nexport const generateMetadata = async ({\n  params,\n}: HomeProps): Promise<Metadata> => {\n  const { locale } = await params;\n  const dictionary = await getDictionary(locale);\n\n  return createMetadata(dictionary.web.home.meta);\n};\n\nconst Home = async ({ params }: HomeProps) => {\n  const { locale } = await params;\n  const dictionary = await getDictionary(locale);\n  const betaFeature = await showBetaFeature();\n\n  return (\n    <>\n      {betaFeature && (\n        <div className=\"w-full bg-black py-2 text-center text-white\">\n          Beta feature now available\n        </div>\n      )}\n      {/* Transparent box wrapper for Hero and TrustedBy sections */}\n      <div className=\"w-full relative\" style={{ backgroundColor: '#161616' }}>\n        <div\n          className=\"max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 -mt-20 pt-20 relative\"\n          style={{\n            border: '1px solid rgba(255, 255, 255, 0.08)',\n            borderTop: 'none',\n            backgroundColor: 'transparent'\n          }}\n        >\n          {/* Subtle centered white gradient */}\n          <div\n            className=\"absolute inset-0 pointer-events-none\"\n            style={{\n              background: 'radial-gradient(ellipse 800px 600px at center, rgba(255, 255, 255, 0.02) 0%, transparent 50%)'\n            }}\n          ></div>\n\n          {/* Colorful horizontal bars background */}\n          <div className=\"absolute inset-0 flex flex-col justify-center overflow-hidden pointer-events-none\">\n            <div className=\"flex flex-col gap-1 h-full w-full justify-center\">\n              {/* Generate horizontal bars on left and right sides */}\n              {[\n                { width: '8%', height: '4px', bg: 'linear-gradient(to right, #ff0844, #ff1744)', segments: 3, position: 'left', top: '20%' },\n                { width: '10%', height: '6px', bg: 'linear-gradient(to right, #e91e63, #ad1457)', segments: 2, position: 'left', top: '25%' },\n                { width: '6%', height: '3px', bg: 'linear-gradient(to right, #9c27b0, #7b1fa2)', segments: 4, position: 'left', top: '30%' },\n                { width: '12%', height: '5px', bg: 'linear-gradient(to right, #673ab7, #512da8)', segments: 2, position: 'left', top: '35%' },\n                { width: '9%', height: '4px', bg: 'linear-gradient(to right, #3f51b5, #303f9f)', segments: 3, position: 'left', top: '40%' },\n                { width: '7%', height: '7px', bg: 'linear-gradient(to right, #2196f3, #1976d2)', segments: 2, position: 'left', top: '45%' },\n                { width: '11%', height: '3px', bg: 'linear-gradient(to right, #00bcd4, #0097a7)', segments: 5, position: 'left', top: '50%' },\n                { width: '9%', height: '5px', bg: 'linear-gradient(to right, #009688, #00695c)', segments: 3, position: 'left', top: '55%' },\n                { width: '8%', height: '6px', bg: 'linear-gradient(to right, #4caf50, #388e3c)', segments: 2, position: 'left', top: '60%' },\n                { width: '10%', height: '4px', bg: 'linear-gradient(to right, #8bc34a, #689f38)', segments: 4, position: 'left', top: '65%' },\n                { width: '9%', height: '5px', bg: 'linear-gradient(to right, #ffeb3b, #fbc02d)', segments: 2, position: 'left', top: '70%' },\n                { width: '7%', height: '3px', bg: 'linear-gradient(to right, #ffc107, #ff8f00)', segments: 3, position: 'left', top: '75%' },\n\n                { width: '9%', height: '5px', bg: 'linear-gradient(to left, #ff9800, #f57c00)', segments: 3, position: 'right', top: '20%' },\n                { width: '11%', height: '4px', bg: 'linear-gradient(to left, #ff5722, #d84315)', segments: 2, position: 'right', top: '25%' },\n                { width: '7%', height: '6px', bg: 'linear-gradient(to left, #f44336, #c62828)', segments: 4, position: 'right', top: '30%' },\n                { width: '8%', height: '3px', bg: 'linear-gradient(to left, #00e676, #00c853)', segments: 5, position: 'right', top: '35%' },\n                { width: '10%', height: '7px', bg: 'linear-gradient(to left, #ff6d00, #ff3d00)', segments: 2, position: 'right', top: '40%' },\n                { width: '12%', height: '4px', bg: 'linear-gradient(to left, #d500f9, #aa00ff)', segments: 3, position: 'right', top: '45%' },\n                { width: '6%', height: '5px', bg: 'linear-gradient(to left, #ff0844, #ff1744)', segments: 2, position: 'right', top: '50%' },\n                { width: '9%', height: '3px', bg: 'linear-gradient(to left, #e91e63, #ad1457)', segments: 4, position: 'right', top: '55%' },\n                { width: '11%', height: '6px', bg: 'linear-gradient(to left, #9c27b0, #7b1fa2)', segments: 2, position: 'right', top: '60%' },\n                { width: '8%', height: '4px', bg: 'linear-gradient(to left, #673ab7, #512da8)', segments: 3, position: 'right', top: '65%' },\n                { width: '10%', height: '5px', bg: 'linear-gradient(to left, #3f51b5, #303f9f)', segments: 2, position: 'right', top: '70%' },\n                { width: '7%', height: '3px', bg: 'linear-gradient(to left, #2196f3, #1976d2)', segments: 4, position: 'right', top: '75%' },\n              ].map((bar, i) => {\n                const isLeftSide = bar.position === 'left';\n\n                let horizontalPosition;\n                if (isLeftSide) {\n                  horizontalPosition = '0%';\n                } else {\n                  horizontalPosition = `${100 - parseInt(bar.width)}%`;\n                }\n\n                return (\n                  <div\n                    key={i}\n                    className=\"absolute opacity-40 flex gap-1\"\n                    style={{\n                      left: horizontalPosition,\n                      top: bar.top,\n                      width: bar.width,\n                      height: bar.height,\n                    }}\n                  >\n                    {/* Create horizontal segments with gaps */}\n                    {Array.from({ length: bar.segments }).map((_, segmentIndex) => (\n                      <div\n                        key={segmentIndex}\n                        className=\"relative\"\n                        style={{\n                          width: `${100 / bar.segments - 1}%`,\n                          height: '100%',\n                          background: bar.bg,\n                        }}\n                      >\n                        {/* Heavy noise texture overlay */}\n                        <div\n                          className=\"absolute inset-0 opacity-90\"\n                          style={{\n                            backgroundImage: `url(\"data:image/svg+xml,%3Csvg viewBox='0 0 256 256' xmlns='http://www.w3.org/2000/svg'%3E%3Cfilter id='noiseFilterHero${i}${segmentIndex}'%3E%3CfeTurbulence type='fractalNoise' baseFrequency='2.2' numOctaves='8' stitchTiles='stitch'/%3E%3CfeColorMatrix values='0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 1 0'/%3E%3C/filter%3E%3Crect width='100%25' height='100%25' filter='url(%23noiseFilterHero${i}${segmentIndex})'/%3E%3C/svg%3E\")`,\n                            mixBlendMode: 'multiply'\n                          }}\n                        />\n                        {/* Additional grain layer */}\n                        <div\n                          className=\"absolute inset-0 opacity-70\"\n                          style={{\n                            backgroundImage: `url(\"data:image/svg+xml,%3Csvg viewBox='0 0 256 256' xmlns='http://www.w3.org/2000/svg'%3E%3Cfilter id='grainFilterHero${i}${segmentIndex}'%3E%3CfeTurbulence type='turbulence' baseFrequency='3.0' numOctaves='10' stitchTiles='stitch'/%3E%3CfeColorMatrix values='0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.9 0'/%3E%3C/filter%3E%3Crect width='100%25' height='100%25' filter='url(%23grainFilterHero${i}${segmentIndex})'/%3E%3C/svg%3E\")`,\n                            mixBlendMode: 'overlay'\n                          }}\n                        />\n                      </div>\n                    ))}\n                  </div>\n                );\n              })}\n            </div>\n          </div>\n\n          {/* Subtle gradient overlay on top of bars */}\n          <div className=\"absolute inset-0 bg-gradient-to-br from-black/30 via-transparent to-black/20 pointer-events-none\" />\n\n          <div className=\"relative z-10\">\n            <Hero dictionary={dictionary} />\n          </div>\n        </div>\n      </div>\n\n      {/* New section attached to hero */}\n      <div className=\"w-full\" style={{ backgroundColor: '#161616' }}>\n        <div\n          className=\"max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 relative overflow-hidden\"\n          style={{\n            backgroundColor: '#161616',\n            border: '1px solid rgba(255, 255, 255, 0.08)',\n            borderTop: 'none'\n          }}\n        >\n            {/* Colorful vertical bars background for entire section */}\n            <div className=\"absolute inset-0 flex items-end justify-center overflow-hidden\">\n              <div className=\"flex items-end gap-2 h-full w-full\">\n                {/* Generate multiple bars with segmented cuts */}\n                {[\n                  { width: '18px', height: '45%', bg: 'linear-gradient(to top, #ff0844, #ff1744, #ff5722)', segments: 3 },\n                  { width: '22px', height: '65%', bg: 'linear-gradient(to top, #e91e63, #ad1457, #880e4f)', segments: 4 },\n                  { width: '16px', height: '35%', bg: 'linear-gradient(to top, #9c27b0, #7b1fa2, #4a148c)', segments: 2 },\n                  { width: '25px', height: '70%', bg: 'linear-gradient(to top, #673ab7, #512da8, #311b92)', segments: 4 },\n                  { width: '19px', height: '50%', bg: 'linear-gradient(to top, #3f51b5, #303f9f, #1a237e)', segments: 3 },\n                  { width: '21px', height: '60%', bg: 'linear-gradient(to top, #2196f3, #1976d2, #0d47a1)', segments: 3 },\n                  { width: '17px', height: '40%', bg: 'linear-gradient(to top, #00bcd4, #0097a7, #006064)', segments: 2 },\n                  { width: '24px', height: '75%', bg: 'linear-gradient(to top, #009688, #00695c, #004d40)', segments: 4 },\n                  { width: '20px', height: '55%', bg: 'linear-gradient(to top, #4caf50, #388e3c, #1b5e20)', segments: 3 },\n                  { width: '23px', height: '65%', bg: 'linear-gradient(to top, #8bc34a, #689f38, #33691e)', segments: 4 },\n                  { width: '18px', height: '45%', bg: 'linear-gradient(to top, #ffeb3b, #fbc02d, #f57f17)', segments: 3 },\n                  { width: '26px', height: '80%', bg: 'linear-gradient(to top, #ffc107, #ff8f00, #e65100)', segments: 4 },\n                  { width: '15px', height: '30%', bg: 'linear-gradient(to top, #ff9800, #f57c00, #e65100)', segments: 2 },\n                  { width: '22px', height: '60%', bg: 'linear-gradient(to top, #ff5722, #d84315, #bf360c)', segments: 3 },\n                  { width: '19px', height: '50%', bg: 'linear-gradient(to top, #f44336, #c62828, #b71c1c)', segments: 3 },\n                  { width: '21px', height: '55%', bg: 'linear-gradient(to top, #00e676, #00c853, #00695c)', segments: 3 },\n                  { width: '17px', height: '40%', bg: 'linear-gradient(to top, #ff6d00, #ff3d00, #dd2c00)', segments: 2 },\n                  { width: '25px', height: '70%', bg: 'linear-gradient(to top, #d500f9, #aa00ff, #6200ea)', segments: 4 },\n                  { width: '20px', height: '55%', bg: 'linear-gradient(to top, #ff0844, #ff1744, #ff5722)', segments: 3 },\n                  { width: '18px', height: '45%', bg: 'linear-gradient(to top, #e91e63, #ad1457, #880e4f)', segments: 3 },\n                  { width: '23px', height: '65%', bg: 'linear-gradient(to top, #9c27b0, #7b1fa2, #4a148c)', segments: 4 },\n                  { width: '16px', height: '35%', bg: 'linear-gradient(to top, #673ab7, #512da8, #311b92)', segments: 2 },\n                  { width: '24px', height: '75%', bg: 'linear-gradient(to top, #3f51b5, #303f9f, #1a237e)', segments: 4 },\n                  { width: '19px', height: '50%', bg: 'linear-gradient(to top, #2196f3, #1976d2, #0d47a1)', segments: 3 },\n                  { width: '22px', height: '60%', bg: 'linear-gradient(to top, #00bcd4, #0097a7, #006064)', segments: 3 },\n                  { width: '17px', height: '40%', bg: 'linear-gradient(to top, #009688, #00695c, #004d40)', segments: 2 },\n                  { width: '26px', height: '80%', bg: 'linear-gradient(to top, #4caf50, #388e3c, #1b5e20)', segments: 4 },\n                  { width: '15px', height: '30%', bg: 'linear-gradient(to top, #8bc34a, #689f38, #33691e)', segments: 2 },\n                  { width: '21px', height: '55%', bg: 'linear-gradient(to top, #ffeb3b, #fbc02d, #f57f17)', segments: 3 },\n                  { width: '18px', height: '45%', bg: 'linear-gradient(to top, #ffc107, #ff8f00, #e65100)', segments: 3 },\n                  { width: '25px', height: '70%', bg: 'linear-gradient(to top, #ff9800, #f57c00, #e65100)', segments: 4 },\n                  { width: '20px', height: '55%', bg: 'linear-gradient(to top, #ff5722, #d84315, #bf360c)', segments: 3 },\n                  { width: '23px', height: '65%', bg: 'linear-gradient(to top, #f44336, #c62828, #b71c1c)', segments: 4 },\n                  { width: '16px', height: '35%', bg: 'linear-gradient(to top, #00e676, #00c853, #00695c)', segments: 2 },\n                  { width: '24px', height: '75%', bg: 'linear-gradient(to top, #ff6d00, #ff3d00, #dd2c00)', segments: 4 },\n                  { width: '19px', height: '50%', bg: 'linear-gradient(to top, #d500f9, #aa00ff, #6200ea)', segments: 3 },\n                  { width: '22px', height: '60%', bg: 'linear-gradient(to top, #ff0844, #ff1744, #ff5722)', segments: 3 },\n                  { width: '17px', height: '40%', bg: 'linear-gradient(to top, #e91e63, #ad1457, #880e4f)', segments: 2 },\n                  { width: '26px', height: '80%', bg: 'linear-gradient(to top, #9c27b0, #7b1fa2, #4a148c)', segments: 4 },\n                  { width: '15px', height: '30%', bg: 'linear-gradient(to top, #673ab7, #512da8, #311b92)', segments: 2 },\n                ].map((bar, i) => (\n                  <div\n                    key={i}\n                    className=\"relative opacity-60 flex flex-col justify-end gap-1\"\n                    style={{\n                      width: bar.width,\n                      height: bar.height,\n                    }}\n                  >\n                    {/* Create segments with gaps */}\n                    {Array.from({ length: bar.segments }).map((_, segmentIndex) => (\n                      <div\n                        key={segmentIndex}\n                        className=\"relative\"\n                        style={{\n                          height: `${100 / bar.segments - 2}%`,\n                          background: bar.bg,\n                        }}\n                      >\n                        {/* Heavy noise texture overlay */}\n                        <div\n                          className=\"absolute inset-0 opacity-90\"\n                          style={{\n                            backgroundImage: `url(\"data:image/svg+xml,%3Csvg viewBox='0 0 256 256' xmlns='http://www.w3.org/2000/svg'%3E%3Cfilter id='noiseFilter${i}${segmentIndex}'%3E%3CfeTurbulence type='fractalNoise' baseFrequency='2.2' numOctaves='8' stitchTiles='stitch'/%3E%3CfeColorMatrix values='0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 1 0'/%3E%3C/filter%3E%3Crect width='100%25' height='100%25' filter='url(%23noiseFilter${i}${segmentIndex})'/%3E%3C/svg%3E\")`,\n                            mixBlendMode: 'multiply'\n                          }}\n                        />\n                        {/* Additional grain layer */}\n                        <div\n                          className=\"absolute inset-0 opacity-70\"\n                          style={{\n                            backgroundImage: `url(\"data:image/svg+xml,%3Csvg viewBox='0 0 256 256' xmlns='http://www.w3.org/2000/svg'%3E%3Cfilter id='grainFilter${i}${segmentIndex}'%3E%3CfeTurbulence type='turbulence' baseFrequency='3.0' numOctaves='10' stitchTiles='stitch'/%3E%3CfeColorMatrix values='0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.9 0'/%3E%3C/filter%3E%3Crect width='100%25' height='100%25' filter='url(%23grainFilter${i}${segmentIndex})'/%3E%3C/svg%3E\")`,\n                            mixBlendMode: 'overlay'\n                          }}\n                        />\n                      </div>\n                    ))}\n                  </div>\n                ))}\n              </div>\n            </div>\n\n            {/* Subtle gradient overlay on top of bars */}\n            <div className=\"absolute inset-0 bg-gradient-to-br from-black/40 via-transparent to-black/30 pointer-events-none\" />\n\n            <div className=\"relative z-10 p-12\">\n              <div className=\"text-center mb-10\">\n                <h2 className=\"text-3xl md:text-4xl text-white leading-tight\">\n                  Code faster and smarter\n                </h2>\n                <p className=\"text-xl md:text-2xl text-white/60 mt-1\">\n                  without leaving your editor\n                </p>\n              </div>\n\n              {/* Feature tabs */}\n              <div className=\"flex justify-center gap-3 mb-6 flex-wrap\">\n                <div className=\"px-3 py-1.5 rounded-full border border-white/20 text-white/60 text-xs\">\n                  AI Assistant\n                </div>\n                <div className=\"px-3 py-1.5 rounded-full border border-white/20 text-white/60 text-xs\">\n                  Code Generation\n                </div>\n                <div className=\"px-3 py-1.5 rounded-full border border-white/20 text-white/60 text-xs\">\n                  Smart Completion\n                </div>\n              </div>\n\n              {/* Feature checkmarks */}\n              <div className=\"flex justify-center gap-6 mb-10 flex-wrap text-xs\">\n                <div className=\"flex items-center gap-1.5 text-white/60\">\n                  <svg className=\"w-3 h-3 text-white/40\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n                    <path fillRule=\"evenodd\" d=\"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z\" clipRule=\"evenodd\" />\n                  </svg>\n                  Real-time Assistance\n                </div>\n                <div className=\"flex items-center gap-1.5 text-white/60\">\n                  <svg className=\"w-3 h-3 text-white/40\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n                    <path fillRule=\"evenodd\" d=\"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z\" clipRule=\"evenodd\" />\n                  </svg>\n                  Context Aware\n                </div>\n                <div className=\"flex items-center gap-1.5 text-white/60\">\n                  <svg className=\"w-3 h-3 text-white/40\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n                    <path fillRule=\"evenodd\" d=\"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z\" clipRule=\"evenodd\" />\n                  </svg>\n                  Multi-Language\n                </div>\n                <div className=\"flex items-center gap-1.5 text-white/60\">\n                  <svg className=\"w-3 h-3 text-white/40\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n                    <path fillRule=\"evenodd\" d=\"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z\" clipRule=\"evenodd\" />\n                  </svg>\n                  Instant Debugging\n                </div>\n                <div className=\"flex items-center gap-1.5 text-white/60\">\n                  <svg className=\"w-3 h-3 text-white/40\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n                    <path fillRule=\"evenodd\" d=\"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z\" clipRule=\"evenodd\" />\n                  </svg>\n                  Easy as typing\n                </div>\n              </div>\n\n              {/* GIF with border */}\n              <div className=\"flex justify-center items-center w-full px-2\">\n                <div className=\"relative border border-white/20 overflow-hidden bg-black/40 backdrop-blur-sm max-w-6xl w-full\">\n                  {/* Window header with dots */}\n                  <div className=\"flex items-center justify-between px-4 py-3 bg-black/20 border-b border-white/10\">\n                    <div className=\"flex items-center gap-2\">\n                      <div className=\"w-3 h-3 rounded-full bg-white/40\"></div>\n                      <div className=\"w-3 h-3 rounded-full bg-white/40\"></div>\n                      <div className=\"w-3 h-3 rounded-full bg-white/40\"></div>\n                    </div>\n                  </div>\n                  <Image\n                    src=\"/images/Cubent-Dev-4.gif\"\n                    alt=\"Cubent Dev Demo\"\n                    width={1400}\n                    height={787}\n                    className=\"w-full h-auto\"\n                    style={{\n                      maxWidth: '100%'\n                    }}\n                    unoptimized\n                  />\n                </div>\n              </div>\n            </div>\n        </div>\n      </div>\n\n      <Mockup />\n\n      <ModelProviders />\n      <Community dictionary={dictionary} />\n      <Download />\n    </>\n  );\n};\n\nexport default Home;\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;AAEA;AACA;AAEA;AAGA;AACA;AACA;;;;;;;;;;;AAaO,MAAM,mBAAmB,OAAO,EACrC,MAAM,EACI;IACV,MAAM,EAAE,MAAM,EAAE,GAAG,MAAM;IACzB,MAAM,aAAa,MAAM,CAAA,GAAA,yIAAA,CAAA,gBAAa,AAAD,EAAE;IAEvC,OAAO,CAAA,GAAA,2HAAA,CAAA,iBAAc,AAAD,EAAE,WAAW,GAAG,CAAC,IAAI,CAAC,IAAI;AAChD;AAEA,MAAM,OAAO,OAAO,EAAE,MAAM,EAAa;IACvC,MAAM,EAAE,MAAM,EAAE,GAAG,MAAM;IACzB,MAAM,aAAa,MAAM,CAAA,GAAA,yIAAA,CAAA,gBAAa,AAAD,EAAE;IACvC,MAAM,cAAc,MAAM,CAAA,GAAA,qIAAA,CAAA,kBAAe,AAAD;IAExC,qBACE;;YACG,6BACC,6VAAC;gBAAI,WAAU;0BAA8C;;;;;;0BAK/D,6VAAC;gBAAI,WAAU;gBAAkB,OAAO;oBAAE,iBAAiB;gBAAU;0BACnE,cAAA,6VAAC;oBACC,WAAU;oBACV,OAAO;wBACL,QAAQ;wBACR,WAAW;wBACX,iBAAiB;oBACnB;;sCAGA,6VAAC;4BACC,WAAU;4BACV,OAAO;gCACL,YAAY;4BACd;;;;;;sCAIF,6VAAC;4BAAI,WAAU;sCACb,cAAA,6VAAC;gCAAI,WAAU;0CAEZ;oCACC;wCAAE,OAAO;wCAAM,QAAQ;wCAAO,IAAI;wCAA+C,UAAU;wCAAG,UAAU;wCAAQ,KAAK;oCAAM;oCAC3H;wCAAE,OAAO;wCAAO,QAAQ;wCAAO,IAAI;wCAA+C,UAAU;wCAAG,UAAU;wCAAQ,KAAK;oCAAM;oCAC5H;wCAAE,OAAO;wCAAM,QAAQ;wCAAO,IAAI;wCAA+C,UAAU;wCAAG,UAAU;wCAAQ,KAAK;oCAAM;oCAC3H;wCAAE,OAAO;wCAAO,QAAQ;wCAAO,IAAI;wCAA+C,UAAU;wCAAG,UAAU;wCAAQ,KAAK;oCAAM;oCAC5H;wCAAE,OAAO;wCAAM,QAAQ;wCAAO,IAAI;wCAA+C,UAAU;wCAAG,UAAU;wCAAQ,KAAK;oCAAM;oCAC3H;wCAAE,OAAO;wCAAM,QAAQ;wCAAO,IAAI;wCAA+C,UAAU;wCAAG,UAAU;wCAAQ,KAAK;oCAAM;oCAC3H;wCAAE,OAAO;wCAAO,QAAQ;wCAAO,IAAI;wCAA+C,UAAU;wCAAG,UAAU;wCAAQ,KAAK;oCAAM;oCAC5H;wCAAE,OAAO;wCAAM,QAAQ;wCAAO,IAAI;wCAA+C,UAAU;wCAAG,UAAU;wCAAQ,KAAK;oCAAM;oCAC3H;wCAAE,OAAO;wCAAM,QAAQ;wCAAO,IAAI;wCAA+C,UAAU;wCAAG,UAAU;wCAAQ,KAAK;oCAAM;oCAC3H;wCAAE,OAAO;wCAAO,QAAQ;wCAAO,IAAI;wCAA+C,UAAU;wCAAG,UAAU;wCAAQ,KAAK;oCAAM;oCAC5H;wCAAE,OAAO;wCAAM,QAAQ;wCAAO,IAAI;wCAA+C,UAAU;wCAAG,UAAU;wCAAQ,KAAK;oCAAM;oCAC3H;wCAAE,OAAO;wCAAM,QAAQ;wCAAO,IAAI;wCAA+C,UAAU;wCAAG,UAAU;wCAAQ,KAAK;oCAAM;oCAE3H;wCAAE,OAAO;wCAAM,QAAQ;wCAAO,IAAI;wCAA8C,UAAU;wCAAG,UAAU;wCAAS,KAAK;oCAAM;oCAC3H;wCAAE,OAAO;wCAAO,QAAQ;wCAAO,IAAI;wCAA8C,UAAU;wCAAG,UAAU;wCAAS,KAAK;oCAAM;oCAC5H;wCAAE,OAAO;wCAAM,QAAQ;wCAAO,IAAI;wCAA8C,UAAU;wCAAG,UAAU;wCAAS,KAAK;oCAAM;oCAC3H;wCAAE,OAAO;wCAAM,QAAQ;wCAAO,IAAI;wCAA8C,UAAU;wCAAG,UAAU;wCAAS,KAAK;oCAAM;oCAC3H;wCAAE,OAAO;wCAAO,QAAQ;wCAAO,IAAI;wCAA8C,UAAU;wCAAG,UAAU;wCAAS,KAAK;oCAAM;oCAC5H;wCAAE,OAAO;wCAAO,QAAQ;wCAAO,IAAI;wCAA8C,UAAU;wCAAG,UAAU;wCAAS,KAAK;oCAAM;oCAC5H;wCAAE,OAAO;wCAAM,QAAQ;wCAAO,IAAI;wCAA8C,UAAU;wCAAG,UAAU;wCAAS,KAAK;oCAAM;oCAC3H;wCAAE,OAAO;wCAAM,QAAQ;wCAAO,IAAI;wCAA8C,UAAU;wCAAG,UAAU;wCAAS,KAAK;oCAAM;oCAC3H;wCAAE,OAAO;wCAAO,QAAQ;wCAAO,IAAI;wCAA8C,UAAU;wCAAG,UAAU;wCAAS,KAAK;oCAAM;oCAC5H;wCAAE,OAAO;wCAAM,QAAQ;wCAAO,IAAI;wCAA8C,UAAU;wCAAG,UAAU;wCAAS,KAAK;oCAAM;oCAC3H;wCAAE,OAAO;wCAAO,QAAQ;wCAAO,IAAI;wCAA8C,UAAU;wCAAG,UAAU;wCAAS,KAAK;oCAAM;oCAC5H;wCAAE,OAAO;wCAAM,QAAQ;wCAAO,IAAI;wCAA8C,UAAU;wCAAG,UAAU;wCAAS,KAAK;oCAAM;iCAC5H,CAAC,GAAG,CAAC,CAAC,KAAK;oCACV,MAAM,aAAa,IAAI,QAAQ,KAAK;oCAEpC,IAAI;oCACJ,IAAI,YAAY;wCACd,qBAAqB;oCACvB,OAAO;wCACL,qBAAqB,GAAG,MAAM,SAAS,IAAI,KAAK,EAAE,CAAC,CAAC;oCACtD;oCAEA,qBACE,6VAAC;wCAEC,WAAU;wCACV,OAAO;4CACL,MAAM;4CACN,KAAK,IAAI,GAAG;4CACZ,OAAO,IAAI,KAAK;4CAChB,QAAQ,IAAI,MAAM;wCACpB;kDAGC,MAAM,IAAI,CAAC;4CAAE,QAAQ,IAAI,QAAQ;wCAAC,GAAG,GAAG,CAAC,CAAC,GAAG,6BAC5C,6VAAC;gDAEC,WAAU;gDACV,OAAO;oDACL,OAAO,GAAG,MAAM,IAAI,QAAQ,GAAG,EAAE,CAAC,CAAC;oDACnC,QAAQ;oDACR,YAAY,IAAI,EAAE;gDACpB;;kEAGA,6VAAC;wDACC,WAAU;wDACV,OAAO;4DACL,iBAAiB,CAAC,uHAAuH,EAAE,IAAI,aAAa,0PAA0P,EAAE,IAAI,aAAa,kBAAkB,CAAC;4DAC5b,cAAc;wDAChB;;;;;;kEAGF,6VAAC;wDACC,WAAU;wDACV,OAAO;4DACL,iBAAiB,CAAC,uHAAuH,EAAE,IAAI,aAAa,2PAA2P,EAAE,IAAI,aAAa,kBAAkB,CAAC;4DAC7b,cAAc;wDAChB;;;;;;;+CAtBG;;;;;uCAZJ;;;;;gCAwCX;;;;;;;;;;;sCAKJ,6VAAC;4BAAI,WAAU;;;;;;sCAEf,6VAAC;4BAAI,WAAU;sCACb,cAAA,6VAAC,mKAAA,CAAA,OAAI;gCAAC,YAAY;;;;;;;;;;;;;;;;;;;;;;0BAMxB,6VAAC;gBAAI,WAAU;gBAAS,OAAO;oBAAE,iBAAiB;gBAAU;0BAC1D,cAAA,6VAAC;oBACC,WAAU;oBACV,OAAO;wBACL,iBAAiB;wBACjB,QAAQ;wBACR,WAAW;oBACb;;sCAGE,6VAAC;4BAAI,WAAU;sCACb,cAAA,6VAAC;gCAAI,WAAU;0CAEZ;oCACC;wCAAE,OAAO;wCAAQ,QAAQ;wCAAO,IAAI;wCAAsD,UAAU;oCAAE;oCACtG;wCAAE,OAAO;wCAAQ,QAAQ;wCAAO,IAAI;wCAAsD,UAAU;oCAAE;oCACtG;wCAAE,OAAO;wCAAQ,QAAQ;wCAAO,IAAI;wCAAsD,UAAU;oCAAE;oCACtG;wCAAE,OAAO;wCAAQ,QAAQ;wCAAO,IAAI;wCAAsD,UAAU;oCAAE;oCACtG;wCAAE,OAAO;wCAAQ,QAAQ;wCAAO,IAAI;wCAAsD,UAAU;oCAAE;oCACtG;wCAAE,OAAO;wCAAQ,QAAQ;wCAAO,IAAI;wCAAsD,UAAU;oCAAE;oCACtG;wCAAE,OAAO;wCAAQ,QAAQ;wCAAO,IAAI;wCAAsD,UAAU;oCAAE;oCACtG;wCAAE,OAAO;wCAAQ,QAAQ;wCAAO,IAAI;wCAAsD,UAAU;oCAAE;oCACtG;wCAAE,OAAO;wCAAQ,QAAQ;wCAAO,IAAI;wCAAsD,UAAU;oCAAE;oCACtG;wCAAE,OAAO;wCAAQ,QAAQ;wCAAO,IAAI;wCAAsD,UAAU;oCAAE;oCACtG;wCAAE,OAAO;wCAAQ,QAAQ;wCAAO,IAAI;wCAAsD,UAAU;oCAAE;oCACtG;wCAAE,OAAO;wCAAQ,QAAQ;wCAAO,IAAI;wCAAsD,UAAU;oCAAE;oCACtG;wCAAE,OAAO;wCAAQ,QAAQ;wCAAO,IAAI;wCAAsD,UAAU;oCAAE;oCACtG;wCAAE,OAAO;wCAAQ,QAAQ;wCAAO,IAAI;wCAAsD,UAAU;oCAAE;oCACtG;wCAAE,OAAO;wCAAQ,QAAQ;wCAAO,IAAI;wCAAsD,UAAU;oCAAE;oCACtG;wCAAE,OAAO;wCAAQ,QAAQ;wCAAO,IAAI;wCAAsD,UAAU;oCAAE;oCACtG;wCAAE,OAAO;wCAAQ,QAAQ;wCAAO,IAAI;wCAAsD,UAAU;oCAAE;oCACtG;wCAAE,OAAO;wCAAQ,QAAQ;wCAAO,IAAI;wCAAsD,UAAU;oCAAE;oCACtG;wCAAE,OAAO;wCAAQ,QAAQ;wCAAO,IAAI;wCAAsD,UAAU;oCAAE;oCACtG;wCAAE,OAAO;wCAAQ,QAAQ;wCAAO,IAAI;wCAAsD,UAAU;oCAAE;oCACtG;wCAAE,OAAO;wCAAQ,QAAQ;wCAAO,IAAI;wCAAsD,UAAU;oCAAE;oCACtG;wCAAE,OAAO;wCAAQ,QAAQ;wCAAO,IAAI;wCAAsD,UAAU;oCAAE;oCACtG;wCAAE,OAAO;wCAAQ,QAAQ;wCAAO,IAAI;wCAAsD,UAAU;oCAAE;oCACtG;wCAAE,OAAO;wCAAQ,QAAQ;wCAAO,IAAI;wCAAsD,UAAU;oCAAE;oCACtG;wCAAE,OAAO;wCAAQ,QAAQ;wCAAO,IAAI;wCAAsD,UAAU;oCAAE;oCACtG;wCAAE,OAAO;wCAAQ,QAAQ;wCAAO,IAAI;wCAAsD,UAAU;oCAAE;oCACtG;wCAAE,OAAO;wCAAQ,QAAQ;wCAAO,IAAI;wCAAsD,UAAU;oCAAE;oCACtG;wCAAE,OAAO;wCAAQ,QAAQ;wCAAO,IAAI;wCAAsD,UAAU;oCAAE;oCACtG;wCAAE,OAAO;wCAAQ,QAAQ;wCAAO,IAAI;wCAAsD,UAAU;oCAAE;oCACtG;wCAAE,OAAO;wCAAQ,QAAQ;wCAAO,IAAI;wCAAsD,UAAU;oCAAE;oCACtG;wCAAE,OAAO;wCAAQ,QAAQ;wCAAO,IAAI;wCAAsD,UAAU;oCAAE;oCACtG;wCAAE,OAAO;wCAAQ,QAAQ;wCAAO,IAAI;wCAAsD,UAAU;oCAAE;oCACtG;wCAAE,OAAO;wCAAQ,QAAQ;wCAAO,IAAI;wCAAsD,UAAU;oCAAE;oCACtG;wCAAE,OAAO;wCAAQ,QAAQ;wCAAO,IAAI;wCAAsD,UAAU;oCAAE;oCACtG;wCAAE,OAAO;wCAAQ,QAAQ;wCAAO,IAAI;wCAAsD,UAAU;oCAAE;oCACtG;wCAAE,OAAO;wCAAQ,QAAQ;wCAAO,IAAI;wCAAsD,UAAU;oCAAE;oCACtG;wCAAE,OAAO;wCAAQ,QAAQ;wCAAO,IAAI;wCAAsD,UAAU;oCAAE;oCACtG;wCAAE,OAAO;wCAAQ,QAAQ;wCAAO,IAAI;wCAAsD,UAAU;oCAAE;oCACtG;wCAAE,OAAO;wCAAQ,QAAQ;wCAAO,IAAI;wCAAsD,UAAU;oCAAE;oCACtG;wCAAE,OAAO;wCAAQ,QAAQ;wCAAO,IAAI;wCAAsD,UAAU;oCAAE;iCACvG,CAAC,GAAG,CAAC,CAAC,KAAK,kBACV,6VAAC;wCAEC,WAAU;wCACV,OAAO;4CACL,OAAO,IAAI,KAAK;4CAChB,QAAQ,IAAI,MAAM;wCACpB;kDAGC,MAAM,IAAI,CAAC;4CAAE,QAAQ,IAAI,QAAQ;wCAAC,GAAG,GAAG,CAAC,CAAC,GAAG,6BAC5C,6VAAC;gDAEC,WAAU;gDACV,OAAO;oDACL,QAAQ,GAAG,MAAM,IAAI,QAAQ,GAAG,EAAE,CAAC,CAAC;oDACpC,YAAY,IAAI,EAAE;gDACpB;;kEAGA,6VAAC;wDACC,WAAU;wDACV,OAAO;4DACL,iBAAiB,CAAC,mHAAmH,EAAE,IAAI,aAAa,sPAAsP,EAAE,IAAI,aAAa,kBAAkB,CAAC;4DACpb,cAAc;wDAChB;;;;;;kEAGF,6VAAC;wDACC,WAAU;wDACV,OAAO;4DACL,iBAAiB,CAAC,mHAAmH,EAAE,IAAI,aAAa,uPAAuP,EAAE,IAAI,aAAa,kBAAkB,CAAC;4DACrb,cAAc;wDAChB;;;;;;;+CArBG;;;;;uCAVJ;;;;;;;;;;;;;;;sCAyCb,6VAAC;4BAAI,WAAU;;;;;;sCAEf,6VAAC;4BAAI,WAAU;;8CACb,6VAAC;oCAAI,WAAU;;sDACb,6VAAC;4CAAG,WAAU;sDAAgD;;;;;;sDAG9D,6VAAC;4CAAE,WAAU;sDAAyC;;;;;;;;;;;;8CAMxD,6VAAC;oCAAI,WAAU;;sDACb,6VAAC;4CAAI,WAAU;sDAAwE;;;;;;sDAGvF,6VAAC;4CAAI,WAAU;sDAAwE;;;;;;sDAGvF,6VAAC;4CAAI,WAAU;sDAAwE;;;;;;;;;;;;8CAMzF,6VAAC;oCAAI,WAAU;;sDACb,6VAAC;4CAAI,WAAU;;8DACb,6VAAC;oDAAI,WAAU;oDAAwB,MAAK;oDAAe,SAAQ;8DACjE,cAAA,6VAAC;wDAAK,UAAS;wDAAU,GAAE;wDAAqH,UAAS;;;;;;;;;;;gDACrJ;;;;;;;sDAGR,6VAAC;4CAAI,WAAU;;8DACb,6VAAC;oDAAI,WAAU;oDAAwB,MAAK;oDAAe,SAAQ;8DACjE,cAAA,6VAAC;wDAAK,UAAS;wDAAU,GAAE;wDAAqH,UAAS;;;;;;;;;;;gDACrJ;;;;;;;sDAGR,6VAAC;4CAAI,WAAU;;8DACb,6VAAC;oDAAI,WAAU;oDAAwB,MAAK;oDAAe,SAAQ;8DACjE,cAAA,6VAAC;wDAAK,UAAS;wDAAU,GAAE;wDAAqH,UAAS;;;;;;;;;;;gDACrJ;;;;;;;sDAGR,6VAAC;4CAAI,WAAU;;8DACb,6VAAC;oDAAI,WAAU;oDAAwB,MAAK;oDAAe,SAAQ;8DACjE,cAAA,6VAAC;wDAAK,UAAS;wDAAU,GAAE;wDAAqH,UAAS;;;;;;;;;;;gDACrJ;;;;;;;sDAGR,6VAAC;4CAAI,WAAU;;8DACb,6VAAC;oDAAI,WAAU;oDAAwB,MAAK;oDAAe,SAAQ;8DACjE,cAAA,6VAAC;wDAAK,UAAS;wDAAU,GAAE;wDAAqH,UAAS;;;;;;;;;;;gDACrJ;;;;;;;;;;;;;8CAMV,6VAAC;oCAAI,WAAU;8CACb,cAAA,6VAAC;wCAAI,WAAU;;0DAEb,6VAAC;gDAAI,WAAU;0DACb,cAAA,6VAAC;oDAAI,WAAU;;sEACb,6VAAC;4DAAI,WAAU;;;;;;sEACf,6VAAC;4DAAI,WAAU;;;;;;sEACf,6VAAC;4DAAI,WAAU;;;;;;;;;;;;;;;;;0DAGnB,6VAAC,4OAAA,CAAA,UAAK;gDACJ,KAAI;gDACJ,KAAI;gDACJ,OAAO;gDACP,QAAQ;gDACR,WAAU;gDACV,OAAO;oDACL,UAAU;gDACZ;gDACA,WAAW;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAQzB,6VAAC,qKAAA,CAAA,SAAM;;;;;0BAEP,6VAAC,iLAAA,CAAA,iBAAc;;;;;0BACf,6VAAC,wKAAA,CAAA,YAAS;gBAAC,YAAY;;;;;;0BACvB,6VAAC,uKAAA,CAAA,WAAQ;;;;;;;AAGf;uCAEe", "debugId": null}}]}